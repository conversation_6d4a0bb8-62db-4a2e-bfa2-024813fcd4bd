package com.huiver.wen.ui.brvah.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.chad.library.adapter4.BaseQuickAdapter
import com.huiver.wen.base.common.data.PageSizeData
import com.huiver.wen.base.net.http.BaseResponse
import com.huiver.wen.base.ui.widget.ViewBindingHolder
import com.huiver.wen.databinding.ItemBrvahBinding
import com.huiver.wen.entities.BravhItem
import com.huiver.wen.ui.brvah.adapter.DemoQuickAdapter
import com.huiver.wen.viewmodel.BasePageViewModel




class BrvahQuickDemoViewModel():BasePageViewModel<BravhItem, ViewBindingHolder<ItemBrvahBinding>>() {
    private val _mDemoLiveData = MutableLiveData<List<BravhItem>>()
    val mDemoLiveData:LiveData<List<BravhItem>> = _mDemoLiveData




    private var page = 1
    override suspend fun onRequestNet(pageData: PageSizeData): BaseResponse<List<BravhItem>> {
        val dataList = mutableListOf<BravhItem>()
        dataList.add(BravhItem("Java$page"))
        dataList.add(BravhItem("C++$page"))
        dataList.add(BravhItem("Kotlin$page"))
        dataList.add(BravhItem("Swift$page"))
        dataList.add(BravhItem("Object C$page"))
        dataList.add(BravhItem("Python$page"))
        dataList.add(BravhItem("Go$page"))
        dataList.add(BravhItem("VB$page"))
        dataList.add(BravhItem(".NET$page"))
        dataList.add(BravhItem("PHP$page"))
        page++
        return BaseResponse(data = dataList)
    }

    override fun createAdapter(): BaseQuickAdapter<BravhItem, ViewBindingHolder<ItemBrvahBinding>> {
        return DemoQuickAdapter()
    }

}