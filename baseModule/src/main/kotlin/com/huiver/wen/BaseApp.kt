package com.huiver.wen

import android.app.Application
import com.huiver.wen.base.module.BuildConfig
import com.huiver.wen.base.net.BaseService
import com.huiver.wen.base.net.NetworkManager
import com.huiver.wen.lib.common.LogUtil

abstract class BaseApp :Application() {
    private val mHttpServiceList: MutableList<BaseService> = mutableListOf()
    override fun onCreate() {
        super.onCreate()
        LogUtil.plant(BuildConfig.DEBUG)
        initHttpService()

    }


    /**
     * 初始化接口服务
     */
    private fun initHttpService(){
        val list = loadHttpServices()
        mHttpServiceList.clear()

        list?.map {
            NetworkManager.createService(it)
        }?.let {
            mHttpServiceList.addAll(it)
        }
    }
    //初始化接口服务
    abstract  fun loadHttpServices(): List<Class<out BaseService>>?



    private fun addHttpService(clazz: Class<BaseService>): BaseService {
        val queryResultService =  queryHttpService(clazz)
        if (queryResultService!=null){
            return  queryResultService
        }

        val t = NetworkManager.createService(clazz)
        mHttpServiceList.add(t)
        return t
    }

    private fun queryHttpService(clazz: Class<BaseService>): BaseService? {
        val result: BaseService? = mHttpServiceList.firstOrNull  { clazz.isInstance(it) }
        return result
    }

    /**
     * 查询服务，如果未初始化，则主动初始化并返回
     */
    fun findHttpService(clazz: Class<BaseService>): BaseService? {
        return addHttpService(clazz)

    }
}