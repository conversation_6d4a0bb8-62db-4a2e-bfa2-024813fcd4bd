package com.huiver.wen.base.net.exception

/**
 * API异常基类
 * @param message 错误信息
 * @param code 错误码
 * @param cause 原始异常
 */
open class ApiException(
    message: String,
    val code: Int = CODE_UNKNOWN_ERROR,
    cause: Throwable? = null
) : Exception(message, cause) {
    
    companion object {
        // 通用错误码
        const val CODE_UNKNOWN_ERROR = -1
        const val CODE_NETWORK_ERROR = -2
        const val CODE_PARSE_ERROR = -3
        const val CODE_TIMEOUT = -4
        const val CODE_NETWORK_UNAVAILABLE = -5
        const val CODE_SSL_ERROR = -6
        const val CODE_IO_ERROR = -7

        // HTTP 4xx 错误码
        const val CODE_BAD_REQUEST = 400
        const val CODE_UNAUTHORIZED = 401
        const val CODE_FORBIDDEN = 403
        const val CODE_NOT_FOUND = 404
        const val CODE_REQUEST_TIMEOUT = 408
        const val CODE_TOO_MANY_REQUESTS = 429
        
        // HTTP 5xx 错误码
        const val CODE_INTERNAL_ERROR = 500
        const val CODE_BAD_GATEWAY = 502
        const val CODE_SERVICE_UNAVAILABLE = 503
        const val CODE_GATEWAY_TIMEOUT = 504
    }
    
    /**
     * 是否为客户端错误
     */
    fun isClientError(): Boolean = code in 400..499
    
    /**
     * 是否为服务器错误
     */
    fun isServerError(): Boolean = code in 500..599
    
    /**
     * 是否为网络错误
     */
    fun isNetworkError(): Boolean = code in listOf(CODE_NETWORK_ERROR, CODE_TIMEOUT)
    
    /**
     * 是否需要重新登录
     */
    fun needReLogin(): Boolean = code == CODE_UNAUTHORIZED
    
    override fun toString(): String {
        return "ApiException(code=$code, message='$message')"
    }
}
