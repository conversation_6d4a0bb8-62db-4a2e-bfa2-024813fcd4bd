package com.huiver.wen.base.net

import com.huiver.wen.base.net.exception.ApiException
import com.huiver.wen.base.net.exception.NetworkException
import com.huiver.wen.base.net.exception.ServerException
import com.huiver.wen.lib.common.LogUtil
import retrofit2.HttpException
import java.io.IOException
import java.net.ConnectException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLException

object ExceptionHandler {

    private const val TAG = "ExceptionHandler"

    /**
     * 处理异常并返回统一的错误结果
     */
    fun handle(e: Throwable): NetworkResult.Error {
        LogUtil.e(TAG, "Network error occurred", e)

        val apiException = when (e) {
            is HttpException -> handleHttpException(e)
            is ConnectException -> NetworkException("网络连接失败，请检查网络设置", ApiException.CODE_NETWORK_ERROR)
            is UnknownHostException -> NetworkException("网络不可用，请检查网络连接", ApiException.CODE_NETWORK_UNAVAILABLE)
            is SocketTimeoutException -> NetworkException("网络请求超时，请稍后重试", ApiException.CODE_TIMEOUT)
            is SSLException -> NetworkException("安全连接失败", ApiException.CODE_SSL_ERROR)
            is IOException -> NetworkException("网络异常，请稍后重试", ApiException.CODE_IO_ERROR)
            is ApiException -> e
            else -> ApiException("未知错误：${e.message}", ApiException.CODE_UNKNOWN_ERROR, e)
        }

        return NetworkResult.Error(apiException)
    }

    /**
     * 处理HTTP异常
     */
    private fun handleHttpException(httpException: HttpException): ApiException {
        return when (httpException.code()) {
            400 -> ApiException("请求参数错误", ApiException.CODE_BAD_REQUEST, httpException)
            401 -> ApiException("身份验证失败，请重新登录", ApiException.CODE_UNAUTHORIZED, httpException)
            403 -> ApiException("访问被拒绝，权限不足", ApiException.CODE_FORBIDDEN, httpException)
            404 -> ApiException("请求的资源不存在", ApiException.CODE_NOT_FOUND, httpException)
            408 -> ApiException("请求超时，请稍后重试", ApiException.CODE_REQUEST_TIMEOUT, httpException)
            500 -> ServerException("服务器内部错误", ApiException.CODE_INTERNAL_ERROR, httpException)
            502 -> ServerException("网关错误", ApiException.CODE_BAD_GATEWAY, httpException)
            503 -> ServerException("服务不可用", ApiException.CODE_SERVICE_UNAVAILABLE, httpException)
            504 -> ServerException("网关超时", ApiException.CODE_GATEWAY_TIMEOUT, httpException)
            in 400..499 -> ApiException("客户端错误(${httpException.code()})", httpException.code(), httpException)
            in 500..599 -> ServerException("服务器错误(${httpException.code()})", httpException.code(), httpException)
            else -> ApiException("HTTP错误(${httpException.code()})", httpException.code(), httpException)
        }
    }
}