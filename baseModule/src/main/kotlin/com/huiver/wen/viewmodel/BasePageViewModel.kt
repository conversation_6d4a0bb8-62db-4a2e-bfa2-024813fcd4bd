package com.huiver.wen.viewmodel

import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter4.BaseQuickAdapter
import com.chad.library.adapter4.QuickAdapterHelper
import com.chad.library.adapter4.loadState.LoadState
import com.chad.library.adapter4.loadState.trailing.TrailingLoadStateAdapter
import com.huiver.wen.base.common.data.PageSizeData
import com.huiver.wen.base.net.http.BaseResponse
import com.huiver.wen.extension.requestIO
import com.huiver.wen.extension.requestMain
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

abstract class BasePageViewModel<T : Any, VH : RecyclerView.ViewHolder> : BaseViewModel() {
    //分页数据
    private val mPageData = PageSizeData()

    //是否在刷新数据
    var mRefreshFlag = true


    private val mAdapter: BaseQuickAdapter<T, VH> by lazy { createAdapter() }

    //组合 Adapter 的帮助类
    private val mAdapterHelper: QuickAdapterHelper by lazy {
        QuickAdapterHelper.Builder(mAdapter)
            .setTrailingLoadStateAdapter(object : TrailingLoadStateAdapter.OnTrailingListener {
                override fun onLoad() {
                    loadData()


                }

                override fun onFailRetry() {
                    loadData()
                }

                override fun isAllowLoading(): Boolean {
                    return !mRefreshFlag
                }
            }).build()
    }



    fun getAdapterHelper(): QuickAdapterHelper {
        return mAdapterHelper
    }

    //协程锁
    private val mutex = Mutex()

    /**
     * 设置pageSize
     */
    protected fun setPageSize(pageSize: Int) {
        mPageData.limit = pageSize
    }

    /**重置分页*/
    private fun resetPageState() {
        mPageData.resetPage()

    }

    /**
     * 重新加载数据
     */
    fun refreshData() {
        mRefreshFlag = true
        loadDataSync(true)
    }

    /**
     * 加载数据
     */
    private fun loadData() {
        loadDataSync(false)
    }

    /**
     * 加载列表数据
     * @param pageData 使用了 copy 函数  防止子类手动修改 pageSize数据
     */
    protected abstract suspend fun onRequestNet(pageData: PageSizeData): BaseResponse<List<T>>


    /**
     *
     *  创建Adapter
     *
     * */
    protected abstract fun createAdapter(): BaseQuickAdapter<T, VH>


    /**
     * RecyclerView绑定Adapter
     */
    fun bindAdapter(recyclerView: RecyclerView) {
        recyclerView.adapter = mAdapterHelper.adapter
    }

    fun getAdapter(): BaseQuickAdapter<T, VH> {
        return mAdapter
    }



    //同步加载数据
    private suspend fun request() {
        val response = onRequestNet(mPageData)
        parseData(response)
    }

    //异步加载
    private fun loadDataSync(reload: Boolean = false) {
        requestIO {
            mutex.withLock {
                if (reload) {
                    resetPageState()
                }
                request()
            }
        }

    }




    private fun parseData(response: BaseResponse<List<T>>) {
        requestMain {
            if (response.isSuccess() && mPageData.getCurrentPage() != 4 && mPageData.getCurrentPage() != 5) {
                if (mRefreshFlag) {
                    mRefreshFlag = false
                }
                val responseData = response.data ?: emptyList()
                mPageData.setIsMore(responseData.size)
                if (mPageData.isFirstPage()) {
                    mAdapter.submitList(responseData)
                } else {
                    mAdapter.addAll(responseData)
                }
                if (responseData.size < mPageData.limit) {
                    // 设置状态为未加载，并且没有分页数据了
                    mAdapterHelper.trailingLoadState = LoadState.NotLoading(true)
                } else {
                    //设置状态为未加载，并且还有分页数据
                    mAdapterHelper.trailingLoadState = LoadState.NotLoading(false)
                }
                mPageData.addCurrentPage()
            } else {
                mAdapterHelper.trailingLoadState = LoadState.Error(Throwable(response.msg))
            }
        }


    }


}